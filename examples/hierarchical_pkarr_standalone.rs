use anyhow::Result;
use ed25519_dalek::{Signing<PERSON><PERSON>, Verifying<PERSON>ey};
use pkarr::{Keypair, PublicKey, SignedPacket};
use std::env;

// Import the conversion functions
use iroh_topic_tracker::integrations::pkarr::{signing_key_to_pkarr_keypair, verifying_key_to_pkarr_public_key};

// Key derivation imports
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};

type HmacSha512 = Hmac<Sha512>;
const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, Clone)]
pub struct ExtendedKey {
    key_left: [u8; 32],
    key_right: [u8; 32], 
    chain_code: [u8; CHAIN_CODE_LENGTH],
    public_key: VerifyingKey,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PublicExtendedKey {
    public_key: VerifyingKey,
    chain_code: [u8; CHAIN_CODE_LENGTH],
}

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidKey,
    CryptoError,
    HardenedDerivationNotSupported,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
            KeyDerivationError::HardenedDerivationNotSupported => write!(f, "Hardened derivation not supported"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

impl ExtendedKey {
    pub fn from_seed(seed: &[u8]) -> Result<Self, KeyDerivationError> {
        let mut mac = HmacSha512::new_from_slice(b"ed25519 seed")
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(seed);
        let master_key = mac.finalize().into_bytes();
        
        let mut key_left = [0u8; 32];
        let mut key_right = [0u8; 32];
        key_left.copy_from_slice(&master_key[..32]);
        key_right.copy_from_slice(&master_key[32..]);
        
        if (key_left[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        Self::clamp_scalar(&mut key_left);
        
        let scalar = Scalar::from_bytes_mod_order(key_left);
        let public_point = &scalar * &ED25519_BASEPOINT_POINT;
        let public_key = VerifyingKey::from_bytes(&public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&master_key);
        let chain_code_hash = chain_hasher.finalize();
        let mut chain_code = [0u8; CHAIN_CODE_LENGTH];
        chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left,
            key_right,
            chain_code,
            public_key,
        })
    }
    
    pub fn derive_child(&self, index: u32) -> Result<ExtendedKey, KeyDerivationError> {
        let is_hardened = index >= HARDENED_OFFSET;
        
        let mut hmac_input = Vec::new();
        if is_hardened {
            hmac_input.push(0x00);
            hmac_input.extend_from_slice(&self.key_left);
            hmac_input.extend_from_slice(&self.key_right);
        } else {
            hmac_input.push(0x02);
            hmac_input.extend_from_slice(self.public_key.as_bytes());
        }
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        let mut kl_new = [0u8; 32];
        let mut kr_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        kr_new.copy_from_slice(&hmac_result[32..]);
        
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        Self::clamp_scalar(&mut kl_new);
        
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        let child_public_point = &child_scalar * &ED25519_BASEPOINT_POINT;
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left: kl_new,
            key_right: kr_new,
            chain_code: child_chain_code,
            public_key: child_public_key,
        })
    }
    
    pub fn signing_key(&self) -> SigningKey {
        SigningKey::from_bytes(&self.key_left)
    }
    
    pub fn to_public(&self) -> PublicExtendedKey {
        PublicExtendedKey {
            public_key: self.public_key,
            chain_code: self.chain_code,
        }
    }
    
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;
        scalar[31] &= 127;
        scalar[31] |= 64;
    }
}

impl PublicExtendedKey {
    pub fn derive_child(&self, index: u32) -> Result<PublicExtendedKey, KeyDerivationError> {
        if index >= HARDENED_OFFSET {
            return Err(KeyDerivationError::HardenedDerivationNotSupported);
        }
        
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(self.public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        let mut kl_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        ExtendedKey::clamp_scalar(&mut kl_new);
        
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        let child_public_point = &child_scalar * &ED25519_BASEPOINT_POINT;
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(PublicExtendedKey {
            public_key: child_public_key,
            chain_code: child_chain_code,
        })
    }
    
    pub fn public_key(&self) -> &VerifyingKey {
        &self.public_key
    }
}

/// Deterministic key finder for handling invalid Ed25519 keys
pub struct DeterministicKeyFinder;

impl DeterministicKeyFinder {
    pub fn find_valid_key<T, F>(
        query: &str,
        derive_fn: F,
        max_attempts: Option<u32>,
    ) -> Result<(u32, T), KeyDerivationError>
    where
        F: Fn(u32) -> Result<T, KeyDerivationError>,
    {
        let max_attempts = max_attempts.unwrap_or(1000);
        
        let mut hasher = Sha256::new();
        hasher.update(query.as_bytes());
        let hash = hasher.finalize();
        let start_index = u32::from_le_bytes([hash[0], hash[1], hash[2], hash[3]]);
        
        for attempt in 0..max_attempts {
            let index = start_index.wrapping_add(attempt);
            
            match derive_fn(index) {
                Ok(key) => return Ok((index, key)),
                Err(KeyDerivationError::InvalidKey) => continue,
                Err(e) => return Err(e),
            }
        }
        
        Err(KeyDerivationError::InvalidKey)
    }
    
    pub fn find_valid_child_key(
        parent: &ExtendedKey,
        query: &str,
        hardened: bool
    ) -> Result<(u32, ExtendedKey), KeyDerivationError> {
        let hardened_offset = if hardened { HARDENED_OFFSET } else { 0 };
        
        Self::find_valid_key(query, |index| {
            parent.derive_child(hardened_offset + index)
        }, None)
    }
    
    pub fn find_valid_public_child_key(
        parent: &PublicExtendedKey,
        query: &str,
    ) -> Result<(u32, PublicExtendedKey), KeyDerivationError> {
        Self::find_valid_key(query, |index| {
            parent.derive_child(index)
        }, None)
    }
}

/// Simple pkarr client for this example
pub struct SimplePkarrClient {
    client: pkarr::Client,
}

impl SimplePkarrClient {
    pub fn new() -> Result<Self> {
        Ok(Self {
            client: pkarr::Client::builder().build()?,
        })
    }
    
    pub async fn publish_query_key(
        &self,
        pkarr_keypair: &Keypair,
        query_public_key: &VerifyingKey,
        chain_code: &[u8; 32],
        subdomain: &str,
    ) -> Result<()> {
        let public_key_hex = hex::encode(query_public_key.as_bytes());
        let chain_code_hex = hex::encode(chain_code);
        let txt_data = format!("ed25519={}:chain={}", public_key_hex, chain_code_hex);

        let signed_packet = SignedPacket::builder()
            .txt(subdomain.try_into().unwrap(), txt_data.as_str().try_into().unwrap(), 300)
            .sign(pkarr_keypair)?;

        self.client.publish(&signed_packet, None).await?;
        Ok(())
    }
    
    pub async fn publish_hello_world(
        &self,
        keypair: &Keypair,
        message: &str,
    ) -> Result<()> {
        let signed_packet = SignedPacket::builder()
            .txt("hello".try_into().unwrap(), message.try_into().unwrap(), 300)
            .sign(keypair)?;
        
        self.client.publish(&signed_packet, None).await?;
        Ok(())
    }
    
    pub async fn resolve_query_key(
        &self,
        pkarr_public_key: &PublicKey,
        subdomain: &str,
    ) -> Result<PublicExtendedKey> {
        let signed_packet = self.client.resolve(pkarr_public_key).await
            .ok_or_else(|| anyhow::anyhow!("No DNS record found"))?;

        for record in signed_packet.resource_records(subdomain) {
            if let pkarr::dns::rdata::RData::TXT(txt_data) = &record.rdata {
                let txt_string: String = txt_data.clone().try_into()
                    .map_err(|e| anyhow::anyhow!("Failed to convert TXT: {:?}", e))?;

                if let Some(data_part) = txt_string.strip_prefix("ed25519=") {
                    let parts: Vec<&str> = data_part.split(":chain=").collect();
                    if parts.len() == 2 {
                        let public_key_bytes = hex::decode(parts[0])?;
                        let chain_code_bytes = hex::decode(parts[1])?;

                        let mut key_bytes = [0u8; 32];
                        let mut chain_code = [0u8; 32];
                        key_bytes.copy_from_slice(&public_key_bytes);
                        chain_code.copy_from_slice(&chain_code_bytes);

                        let public_key = VerifyingKey::from_bytes(&key_bytes)?;
                        return Ok(PublicExtendedKey { public_key, chain_code });
                    }
                }
            }
        }

        Err(anyhow::anyhow!("No ed25519 public key found"))
    }
    
    pub async fn resolve_hello_world(&self, public_key: &PublicKey) -> Result<String> {
        let signed_packet = self.client.resolve(public_key).await
            .ok_or_else(|| anyhow::anyhow!("No DNS record found"))?;
        
        for record in signed_packet.resource_records("hello") {
            if let pkarr::dns::rdata::RData::TXT(txt_data) = &record.rdata {
                let txt_string: String = txt_data.clone().try_into()
                    .map_err(|e| anyhow::anyhow!("Failed to convert TXT: {:?}", e))?;
                return Ok(txt_string);
            }
        }
        
        Err(anyhow::anyhow!("No hello record found"))
    }
}

async fn server_mode() -> Result<()> {
    println!("=== SERVER MODE ===");
    
    // 1. Generate root pkarr keypair
    let pkarr_keypair = Keypair::random();
    println!("1. Root pkarr keypair: {}", pkarr_keypair.public_key().to_z32());
    
    // 2. Derive query master key (hardened)
    let seed = pkarr_keypair.secret_key();
    let root_key = ExtendedKey::from_seed(&seed)?;
    let (query_index, query_key) = DeterministicKeyFinder::find_valid_child_key(&root_key, "query-master", true)?;
    println!("2. Query master key derived at index: {}", query_index);
    
    // 3. Publish query master public key to pkarr
    let pkarr_client = SimplePkarrClient::new()?;
    pkarr_client.publish_query_key(&pkarr_keypair, &query_key.public_key, &query_key.chain_code, "query-master").await?;
    println!("3. ✓ Published query master public key to pkarr");
    
    // 4. Derive specific query key for "hello world" data
    let (hello_index, hello_key) = DeterministicKeyFinder::find_valid_child_key(&query_key, "hello-world", false)?;
    println!("4. Hello world key derived at index: {}", hello_index);
    
    // 5. Publish "hello world" under the derived key
    let hello_keypair = signing_key_to_pkarr_keypair(&hello_key.signing_key());
    pkarr_client.publish_hello_world(&hello_keypair, "Hello World from Hierarchical Pkarr!").await?;
    println!("5. ✓ Published 'Hello World' under query key: {}", hello_keypair.public_key().to_z32());
    println!("   Ed25519 public key: {}", hex::encode(hello_key.public_key.as_bytes()));
    
    println!("\n📋 For client mode, use:");
    println!("   Root pkarr key: {}", pkarr_keypair.public_key().to_z32());
    
    Ok(())
}

async fn client_mode(root_pkarr_key: &str) -> Result<()> {
    println!("=== CLIENT MODE ===");
    
    // 1. Parse root pkarr public key
    let pkarr_public_key = PublicKey::try_from(root_pkarr_key)?;
    println!("1. Using root pkarr key: {}", pkarr_public_key.to_z32());
    
    // 2. Resolve query master public key from pkarr
    let pkarr_client = SimplePkarrClient::new()?;
    let query_master_extended = pkarr_client.resolve_query_key(&pkarr_public_key, "query-master").await?;
    println!("2. ✓ Resolved query master public key and chain code from pkarr");
    
    // 3. Derive the same "hello world" query key (public key only)
    let (hello_index, hello_public_key) = DeterministicKeyFinder::find_valid_public_child_key(&query_master_extended, "hello-world")?;
    println!("3. ✓ Derived hello world public key at index: {}", hello_index);
    println!("   Ed25519 public key: {}", hex::encode(hello_public_key.public_key().as_bytes()));
    

    // 4. Convert to pkarr public key and resolve the hello world record
    let hello_pkarr_key = PublicKey::try_from(hello_public_key.public_key().as_bytes()).expect("Valid Ed25519 public key");
    println!("4. Attempting to resolve hello world record from: {}", hello_pkarr_key.to_z32());

    // Try to resolve with retries (pkarr records may take time to propagate)
    let mut attempts = 0;
    let max_attempts = 5;
    loop {
        attempts += 1;
        match pkarr_client.resolve_hello_world(&hello_pkarr_key).await {
            Ok(hello_message) => {
                println!("   ✓ Resolved hello world message: '{}'", hello_message);
                break;
            }
            Err(e) if attempts < max_attempts => {
                println!("   Attempt {}/{} failed: {}. Retrying in 2 seconds...", attempts, max_attempts, e);
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
            }
            Err(e) => {
                println!("   ✗ Failed to resolve after {} attempts: {}", max_attempts, e);
                println!("   Note: pkarr records may take time to propagate across the network");
                break;
            }
        }
    }
    
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    let args: Vec<String> = env::args().collect();
    
    if args.len() < 2 {
        println!("Usage:");
        println!("  {} server                    # Run server mode", args[0]);
        println!("  {} client <root_pkarr_key>   # Run client mode", args[0]);
        return Ok(());
    }
    
    match args[1].as_str() {
        "server" => server_mode().await,
        "client" => {
            if args.len() < 3 {
                println!("Error: Client mode requires root pkarr key");
                println!("Usage: {} client <root_pkarr_key>", args[0]);
                return Ok(());
            }
            client_mode(&args[2]).await
        }
        _ => {
            println!("Error: Unknown mode '{}'", args[1]);
            println!("Use 'server' or 'client'");
            Ok(())
        }
    }
}
