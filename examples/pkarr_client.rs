use anyhow::Result;
use ed25519_dalek::VerifyingKey;
use iroh_topic_tracker::integrations::pkarr::PkarrClient;
use pkarr::PublicKey;

// For public key derivation, we need these imports
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT, edwards::CompressedEdwardsY};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};

type HmacSha512 = Hmac<Sha512>;

const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, <PERSON>lone)]
pub struct PublicExtendedKey {
    public_key: VerifyingKey,
    chain_code: [u8; CHAIN_CODE_LENGTH],
}

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidKey,
    InvalidChildIndex,
    CryptoError,
    KeyAtInfinity,
    HardenedDerivationNotSupported,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key (third highest bit set)"),
            KeyDerivationError::InvalidChildIndex => write!(f, "Invalid child index"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
            KeyDerivationError::KeyAtInfinity => write!(f, "Key at infinity"),
            KeyDerivationError::HardenedDerivationNotSupported => write!(f, "Hardened derivation not supported for public keys"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

impl PublicExtendedKey {
    /// Create a public extended key from a root public key and chain code
    /// This simulates what a client would have: the root public key and chain code
    pub fn from_public_key_and_chain_code(public_key: VerifyingKey, chain_code: [u8; 32]) -> Self {
        Self {
            public_key,
            chain_code,
        }
    }
    
    /// Derive child public key at given index (non-hardened only)
    pub fn derive_child(&self, index: u32) -> Result<PublicExtendedKey, KeyDerivationError> {
        if index >= HARDENED_OFFSET {
            return Err(KeyDerivationError::HardenedDerivationNotSupported);
        }
        
        // Non-hardened derivation: 0x02 || A || index
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(self.public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        // Compute HMAC
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        // Split result
        let mut kl_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        
        // Check third highest bit requirement
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        // Apply Ed25519 bit manipulations
        Self::clamp_scalar(&mut kl_new);
        
        // Generate child scalar
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        
        // For public key derivation: child_public = parent_public + child_scalar * G
        let compressed_parent = CompressedEdwardsY(*self.public_key.as_bytes());
        let parent_point = compressed_parent.decompress()
            .ok_or(KeyDerivationError::CryptoError)?;
        let child_public_point = parent_point + &child_scalar * &ED25519_BASEPOINT_POINT;
        
        // Check if public key is identity point (should discard)
        if child_public_point.compress().to_bytes() == [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] {
            return Err(KeyDerivationError::KeyAtInfinity);
        }
        
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        // Generate child chain code
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(PublicExtendedKey {
            public_key: child_public_key,
            chain_code: child_chain_code,
        })
    }
    
    /// Derive child key from derivation path (non-hardened only)
    pub fn derive_path(&self, path: &str) -> Result<PublicExtendedKey, KeyDerivationError> {
        let path = path.strip_prefix("m/").unwrap_or(path);
        let mut current_key = self.clone();
        
        for component in path.split('/') {
            if component.is_empty() {
                continue;
            }
            
            // Check if it's hardened (ends with ' or h)
            if component.ends_with('\'') || component.ends_with('h') {
                return Err(KeyDerivationError::HardenedDerivationNotSupported);
            }
            
            let index: u32 = component.parse()
                .map_err(|_| KeyDerivationError::InvalidChildIndex)?;
            
            current_key = current_key.derive_child(index)?;
        }
        
        Ok(current_key)
    }
    
    /// Get the public key
    pub fn public_key(&self) -> &VerifyingKey {
        &self.public_key
    }
    
    /// Apply Ed25519 scalar clamping
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;  // Clear bottom 3 bits
        scalar[31] &= 127; // Clear top bit
        scalar[31] |= 64;  // Set second highest bit
    }
}

/// Deterministic key finder for client-side public key derivation
pub struct DeterministicPublicKeyFinder;

impl DeterministicPublicKeyFinder {
    /// Find the first valid public key index for a given query string
    pub fn find_valid_public_key(
        query: &str,
        derive_fn: impl Fn(u32) -> Result<PublicExtendedKey, KeyDerivationError>,
        max_attempts: Option<u32>,
    ) -> Result<(u32, PublicExtendedKey), KeyDerivationError> {
        let max_attempts = max_attempts.unwrap_or(1000);

        // Hash the query string to get a deterministic starting point (same as server)
        let mut hasher = Sha256::new();
        hasher.update(query.as_bytes());
        let hash = hasher.finalize();

        // Use first 4 bytes of hash as starting index
        let start_index = u32::from_le_bytes([hash[0], hash[1], hash[2], hash[3]]);

        // Try indices starting from the hash-derived index
        for attempt in 0..max_attempts {
            let index = start_index.wrapping_add(attempt);

            match derive_fn(index) {
                Ok(key) => {
                    println!("   Found valid public key for '{}' at index {} (attempt {})", query, index, attempt + 1);
                    return Ok((index, key));
                }
                Err(KeyDerivationError::InvalidKey) => {
                    // Continue to next index
                    continue;
                }
                Err(e) => {
                    // Other errors are not retryable
                    return Err(e);
                }
            }
        }

        Err(KeyDerivationError::InvalidKey)
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Hierarchical Pkarr Query Key Resolution ===");

    // Step 1: Resolve the query master public key from pkarr
    let pkarr_public_key_str = "6q4oy7sssccbp845o4m9rsfjif77q7ga8mw5ib1p5pn89hygtcuo"; // From latest hierarchical_pkarr run
    let pkarr_public_key = PublicKey::try_from(pkarr_public_key_str)?;

    println!("1. Using pkarr public key: {}", pkarr_public_key.to_z32());

    let pkarr_client = PkarrClient::new()?;

    // Step 2: Resolve the query master public key (this was derived with hardened path m/0')
    let query_master_public_key = match pkarr_client.resolve_query_key(&pkarr_public_key, Some("query-master")).await {
        Ok(key) => {
            println!("2. ✓ Resolved query master public key from pkarr");
            println!("   Query master key: {}", hex::encode(key.as_bytes()));
            key
        }
        Err(e) => {
            println!("2. ✗ Failed to resolve query master key: {}", e);
            println!("   Run the pkarr_workflow example first to publish the key");
            return Ok(());
        }
    };

    // Step 3: Now we can derive specific query keys for different purposes using non-hardened derivation
    // The server would publish the chain code separately, or use a standard one
    // For this demo, we'll use a known chain code (in practice, this might be published or standardized)
    let chain_code = [42u8; 32]; // This would be communicated through the protocol

    let query_master_extended = PublicExtendedKey::from_public_key_and_chain_code(
        query_master_public_key,
        chain_code
    );

    println!("3. Created extended public key for non-hardened derivation");

    // Step 4: Derive specific query keys for different purposes using deterministic method
    let purposes = vec![
        "user-data",
        "messages",
        "metadata",
        "files",
        "social",
    ];

    println!("4. Deriving specific query keys (deterministic, same as server):");

    for purpose in purposes {
        match DeterministicPublicKeyFinder::find_valid_public_key(
            purpose,
            |index| query_master_extended.derive_child(index),
            None
        ) {
            Ok((index, derived_key)) => {
                println!("   ✓ {}: m/{} → {}",
                    purpose,
                    index,
                    hex::encode(derived_key.public_key().as_bytes())[..16].to_string() + "..."
                );

                // Now the client can use this derived public key to resolve pkarr records
                // For example: resolve pkarr records published under this derived key
                println!("     → Can resolve pkarr records for {}", purpose);
            }
            Err(e) => {
                println!("   ✗ {}: Failed to derive - {}", purpose, e);
            }
        }
    }

    println!("\n=== Security Analysis ===");
    println!("✓ Server can derive private keys for all purposes (has master private key)");
    println!("✓ Client can derive public keys for all purposes (has master public key)");
    println!("✓ Client uses SAME deterministic derivation as server (identical indices)");
    println!("✗ Client CANNOT derive private keys (hardened derivation protects master)");
    println!("✗ Client CANNOT overwrite pkarr records (no private keys for derived purposes)");

    println!("\n=== Use Case Example ===");
    println!("1. Server publishes user data under derived key for 'user-data' (m/0)");
    println!("2. Server publishes messages under derived key for 'messages' (m/1)");
    println!("3. Client derives same public keys and resolves both record types");
    println!("4. Client cannot modify any records (no private keys)");

    // Demonstrate that we can derive many keys without the third-bit issue
    println!("\n=== Demonstrating Multiple Derivations ===");
    let mut successful_derivations = 0;
    for i in 0..20 {
        match query_master_extended.derive_child(i) {
            Ok(_) => {
                successful_derivations += 1;
            }
            Err(_) => {
                // Skip invalid keys due to third highest bit
            }
        }
    }
    println!("Successfully derived {}/20 keys (some skipped due to third highest bit rule)", successful_derivations);

    Ok(())
}
